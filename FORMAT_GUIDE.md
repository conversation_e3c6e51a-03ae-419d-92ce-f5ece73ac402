# 格式保持功能使用指南

## 概述

Chrome扩展现在支持保持网页文本的原始格式，包括段落、列表、代码块、文本样式等。当你选择网页内容并保存到Flomo时，格式将被自动转换为Markdown格式以保持可读性。

## 支持的格式

### 1. 标题层级
- **H1-H6标题** → `# ## ### #### ##### ######`
- 自动保持标题的层级关系
- 标题前后会添加适当的空行

### 2. 段落和换行
- **段落** → 段落之间用双换行分隔
- **换行符** → 保持原始换行
- **空白字符** → 清理多余空白，保持必要缩进

### 3. 列表结构
- **无序列表** → `- 项目` 或 `* 项目`（根据层级交替）
- **有序列表** → `1. 项目`
- **嵌套列表** → 自动缩进（每级2个空格）
- **混合列表** → 支持有序和无序列表混合嵌套

### 4. 代码格式
- **行内代码** → `` `代码` ``
- **代码块** → 
  ```
  ```
  代码内容
  ```
  ```
- **保持缩进** → 代码块内的原始缩进和格式完全保持

### 5. 引用块
- **引用** → `> 引用内容`
- **多行引用** → 每行都添加 `>` 前缀
- **嵌套引用** → 支持多层引用

### 6. 文本样式
- **粗体** → `**文本**`（支持 `<strong>` 和 `<b>`）
- **斜体** → `*文本*`（支持 `<em>` 和 `<i>`）
- **下划线** → `<u>文本</u>`
- **删除线** → `~~文本~~`（支持 `<del>` 和 `<s>`）
- **高亮** → `==文本==`（`<mark>`）
- **上标** → `^文本^`（`<sup>`）
- **下标** → `~文本~`（`<sub>`）
- **键盘按键** → `` `按键` ``（`<kbd>`）

### 7. 链接
- **普通链接** → `[链接文本](URL)`
- **邮件链接** → `[邮件地址](mailto:email)`
- **相同文本链接** → 直接显示URL

### 8. 其他元素
- **分隔线** → `---`
- **表格** → 简化的表格格式（保持基本结构）

## 使用方法

### 1. 基本使用
1. 在网页上选择要保存的内容
2. 右键选择"保存到Flomo"
3. 扩展会自动打开侧边栏，显示格式化后的内容
4. 可以在侧边栏中进一步编辑
5. 点击"保存到Flomo"完成保存

### 2. 格式化效果
选择前（HTML）：
```html
<h2>项目介绍</h2>
<p>这是一个<strong>重要</strong>的项目。</p>
<ul>
  <li>功能1</li>
  <li>功能2
    <ul>
      <li>子功能A</li>
    </ul>
  </li>
</ul>
```

选择后（Markdown）：
```markdown
## 项目介绍

这是一个**重要**的项目。

- 功能1
- 功能2
  - 子功能A
```

### 3. 测试功能
1. 打开 `format-test.html` 测试页面
2. 点击任意测试区域选择内容
3. 使用右键菜单测试格式保持效果
4. 或者点击"🧪 测试格式化功能"按钮在控制台查看结果

## 技术实现

### 核心函数
- `htmlToFormattedText()` - 将HTML转换为格式化文本
- `processElement()` - 递归处理HTML元素
- `preserveCodeFormatting()` - 保持代码块格式
- `getListIndent()` - 计算列表缩进
- `getListLevel()` - 获取列表层级

### 处理流程
1. 获取选中内容的HTML结构
2. 递归遍历所有DOM节点
3. 根据元素类型应用相应的格式化规则
4. 清理多余空白和换行
5. 返回格式化的Markdown文本

## 注意事项

### 1. 兼容性
- 向后兼容：如果格式化失败，会自动降级到纯文本
- 渐进增强：新功能不影响现有的基本保存功能

### 2. 性能优化
- 防止无限循环：列表层级检测有深度限制
- 内存管理：使用临时DOM元素，及时清理
- 错误处理：格式化异常时自动使用原始文本

### 3. 格式限制
- 复杂表格可能简化显示
- 某些CSS样式无法转换
- 图片和媒体内容会被忽略

## 故障排除

### 1. 格式化不生效
- 检查content.js是否正确加载
- 确认扩展权限设置正确
- 查看浏览器控制台是否有错误信息

### 2. 格式错乱
- 可能是网页HTML结构复杂
- 尝试选择更小的内容块
- 检查是否有嵌套过深的元素

### 3. 性能问题
- 避免选择过大的内容区域
- 复杂页面可能需要更多处理时间
- 如有问题会自动降级到纯文本模式

## 更新日志

### v2.0
- ✅ 新增格式保持功能
- ✅ 支持Markdown转换
- ✅ 保持列表层级结构
- ✅ 代码块格式保持
- ✅ 文本样式转换
- ✅ 向后兼容性保证
